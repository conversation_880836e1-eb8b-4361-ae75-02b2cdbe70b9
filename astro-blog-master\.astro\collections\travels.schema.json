{"$ref": "#/definitions/travels", "definitions": {"travels": {"type": "object", "properties": {"title": {"type": "string"}, "published": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "subtitle": {"type": "string"}, "posttitle": {"type": "string"}, "coverImage": {"type": "string"}, "draft": {"type": "boolean", "default": false}, "lang": {"type": "string", "enum": ["", "zh", "en", "es", "ja", "ru", "zh-tw"], "default": ""}, "abbrlink": {"type": "string", "default": ""}, "description": {"type": "string"}, "days": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string"}, "descriptions": {"type": "array", "items": {"type": "string"}}, "photos": {"type": "array", "items": {"type": "object", "properties": {"src": {"type": "string"}, "alt": {"type": "string"}, "lat": {"type": "number"}, "lng": {"type": "number"}, "caption": {"type": "string"}}, "required": ["src", "alt", "caption"], "additionalProperties": false}}}, "required": ["title", "descriptions", "photos"], "additionalProperties": false}}, "$schema": {"type": "string"}}, "required": ["title", "published", "subtitle", "posttitle", "coverImage", "description", "days"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}