{"version": 3, "sources": ["../../lite-youtube-embed/src/lite-yt-embed.js"], "sourcesContent": ["/**\n * A lightweight youtube embed. Still should feel the same to the user, just MUCH faster to initialize and paint.\n *\n * Thx to these as the inspiration\n *   https://storage.googleapis.com/amp-vs-non-amp/youtube-lazy.html\n *   https://autoplay-youtube-player.glitch.me/\n *\n * Once built it, I also found these:\n *   https://github.com/ampproject/amphtml/blob/master/extensions/amp-youtube (👍👍)\n *   https://github.com/Daugilas/lazyYT\n *   https://github.com/vb/lazyframe\n */\nclass LiteYTEmbed extends HTMLElement {\n    connectedCallback() {\n        this.videoId = this.getAttribute('videoid');\n\n        let playBtnEl = this.querySelector('.lty-playbtn');\n        // A label for the button takes priority over a [playlabel] attribute on the custom-element\n        this.playLabel = (playBtnEl && playBtnEl.textContent.trim()) || this.getAttribute('playlabel') || 'Play';\n\n        this.dataset.title = this.getAttribute('title') || \"\";\n\n        /**\n         * Lo, the youtube poster image!  (aka the thumbnail, image placeholder, etc)\n         *\n         * See https://github.com/paulirish/lite-youtube-embed/blob/master/youtube-thumbnail-urls.md\n         */\n        if (!this.style.backgroundImage) {\n          this.style.backgroundImage = `url(\"https://i.ytimg.com/vi/${this.videoId}/hqdefault.jpg\")`;\n          this.upgradePosterImage();\n        }\n\n        // Set up play button, and its visually hidden label\n        if (!playBtnEl) {\n            playBtnEl = document.createElement('button');\n            playBtnEl.type = 'button';\n            playBtnEl.classList.add('lty-playbtn');\n            this.append(playBtnEl);\n        }\n        if (!playBtnEl.textContent) {\n            const playBtnLabelEl = document.createElement('span');\n            playBtnLabelEl.className = 'lyt-visually-hidden';\n            playBtnLabelEl.textContent = this.playLabel;\n            playBtnEl.append(playBtnLabelEl);\n        }\n\n        this.addNoscriptIframe();\n\n        // for the PE pattern, change anchor's semantics to button\n        if(playBtnEl.nodeName === 'A'){\n            playBtnEl.removeAttribute('href');\n            playBtnEl.setAttribute('tabindex', '0');\n            playBtnEl.setAttribute('role', 'button');\n            // fake button needs keyboard help\n            playBtnEl.addEventListener('keydown', e => {\n                if( e.key === 'Enter' || e.key === ' ' ){\n                    e.preventDefault();\n                    this.activate();\n                }\n            });\n        }\n\n        // On hover (or tap), warm up the TCP connections we're (likely) about to use.\n        this.addEventListener('pointerover', LiteYTEmbed.warmConnections, {once: true});\n        this.addEventListener('focusin', LiteYTEmbed.warmConnections, {once: true});\n\n        // Once the user clicks, add the real iframe and drop our play button\n        // TODO: In the future we could be like amp-youtube and silently swap in the iframe during idle time\n        //   We'd want to only do this for in-viewport or near-viewport ones: https://github.com/ampproject/amphtml/pull/5003\n        this.addEventListener('click', this.activate);\n\n        // Chrome & Edge desktop have no problem with the basic YouTube Embed with ?autoplay=1\n        // However Safari desktop and most/all mobile browsers do not successfully track the user gesture of clicking through the creation/loading of the iframe,\n        // so they don't autoplay automatically. Instead we must load an additional 2 sequential JS files (1KB + 165KB) (un-br) for the YT Player API\n        // TODO: Try loading the the YT API in parallel with our iframe and then attaching/playing it. #82\n        this.needsYTApi = this.hasAttribute(\"js-api\") || navigator.vendor.includes('Apple') || navigator.userAgent.includes('Mobi');\n    }\n\n    /**\n     * Add a <link rel={preload | preconnect} ...> to the head\n     */\n    static addPrefetch(kind, url, as) {\n        const linkEl = document.createElement('link');\n        linkEl.rel = kind;\n        linkEl.href = url;\n        if (as) {\n            linkEl.as = as;\n        }\n        document.head.append(linkEl);\n    }\n\n    /**\n     * Begin pre-connecting to warm up the iframe load\n     * Since the embed's network requests load within its iframe,\n     *   preload/prefetch'ing them outside the iframe will only cause double-downloads.\n     * So, the best we can do is warm up a few connections to origins that are in the critical path.\n     *\n     * Maybe `<link rel=preload as=document>` would work, but it's unsupported: http://crbug.com/593267\n     * But TBH, I don't think it'll happen soon with Site Isolation and split caches adding serious complexity.\n     */\n    static warmConnections() {\n        if (LiteYTEmbed.preconnected) return;\n\n        // The iframe document and most of its subresources come right off youtube.com\n        LiteYTEmbed.addPrefetch('preconnect', 'https://www.youtube-nocookie.com');\n        // The botguard script is fetched off from google.com\n        LiteYTEmbed.addPrefetch('preconnect', 'https://www.google.com');\n\n        // Not certain if these ad related domains are in the critical path. Could verify with domain-specific throttling.\n        LiteYTEmbed.addPrefetch('preconnect', 'https://googleads.g.doubleclick.net');\n        LiteYTEmbed.addPrefetch('preconnect', 'https://static.doubleclick.net');\n\n        LiteYTEmbed.preconnected = true;\n    }\n\n    fetchYTPlayerApi() {\n        if (window.YT || (window.YT && window.YT.Player)) return;\n\n        this.ytApiPromise = new Promise((res, rej) => {\n            var el = document.createElement('script');\n            el.src = 'https://www.youtube.com/iframe_api';\n            el.async = true;\n            el.onload = _ => {\n                YT.ready(res);\n            };\n            el.onerror = rej;\n            this.append(el);\n        });\n    }\n\n    /** Return the YT Player API instance. (Public L-YT-E API) */\n    async getYTPlayer() {\n        if(!this.playerPromise) {\n            await this.activate();\n        }\n\n        return this.playerPromise;\n    }\n\n    async addYTPlayerIframe() {\n        this.fetchYTPlayerApi();\n        await this.ytApiPromise;\n\n        const videoPlaceholderEl = document.createElement('div')\n        this.append(videoPlaceholderEl);\n\n        const paramsObj = Object.fromEntries(this.getParams().entries());\n\n        this.playerPromise = new Promise(resolve => {\n            let player = new YT.Player(videoPlaceholderEl, {\n                width: '100%',\n                videoId: this.videoId,\n                playerVars: paramsObj,\n                events: {\n                    'onReady': event => {\n                        event.target.playVideo();\n                        resolve(player);\n                    }\n                }\n            });\n        });\n    }\n\n    // Add the iframe within <noscript> for indexability discoverability. See https://github.com/paulirish/lite-youtube-embed/issues/105\n    addNoscriptIframe() {\n        const iframeEl = this.createBasicIframe();\n        const noscriptEl = document.createElement('noscript');\n        // Appending into noscript isn't equivalant for mysterious reasons: https://html.spec.whatwg.org/multipage/scripting.html#the-noscript-element\n        noscriptEl.innerHTML = iframeEl.outerHTML;\n        this.append(noscriptEl);\n    }\n\n    getParams() {\n        const params = new URLSearchParams(this.getAttribute('params') || []);\n        params.append('autoplay', '1');\n        params.append('playsinline', '1');\n        return params;\n    }\n\n    async activate(){\n        if (this.classList.contains('lyt-activated')) return;\n        this.classList.add('lyt-activated');\n\n        if (this.needsYTApi) {\n            return this.addYTPlayerIframe(this.getParams());\n        }\n\n        const iframeEl = this.createBasicIframe();\n        this.append(iframeEl);\n\n        // Set focus for a11y\n        iframeEl.focus();\n    }\n\n    createBasicIframe(){\n        const iframeEl = document.createElement('iframe');\n        iframeEl.width = 560;\n        iframeEl.height = 315;\n        // No encoding necessary as [title] is safe. https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html#:~:text=Safe%20HTML%20Attributes%20include\n        iframeEl.title = this.playLabel;\n        iframeEl.allow = 'accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture';\n        iframeEl.allowFullscreen = true;\n        // AFAIK, the encoding here isn't necessary for XSS, but we'll do it only because this is a URL\n        // https://stackoverflow.com/q/64959723/89484\n        iframeEl.src = `https://www.youtube-nocookie.com/embed/${encodeURIComponent(this.videoId)}?${this.getParams().toString()}`;\n        return iframeEl;\n    }\n\n    /**\n     * In the spirit of the `lowsrc` attribute and progressive JPEGs, we'll upgrade the reliable\n     * poster image to a higher resolution one, if it's available.\n     * Interestingly this sddefault webp is often smaller in filesize, but we will still attempt it second\n     * because getting _an_ image in front of the user if our first priority.\n     *\n     * See https://github.com/paulirish/lite-youtube-embed/blob/master/youtube-thumbnail-urls.md for more details\n     */\n    upgradePosterImage() {\n         // Defer to reduce network contention.\n        setTimeout(() => {\n            const webpUrl = `https://i.ytimg.com/vi_webp/${this.videoId}/sddefault.webp`;\n            const img = new Image();\n            img.fetchPriority = 'low'; // low priority to reduce network contention\n            img.referrerpolicy = 'origin'; // Not 100% sure it's needed, but https://github.com/ampproject/amphtml/pull/3940\n            img.src = webpUrl;\n            img.onload = e => {\n                // A pretty ugly hack since onerror won't fire on YouTube image 404. This is (probably) due to\n                // Youtube's style of returning data even with a 404 status. That data is a 120x90 placeholder image.\n                // … per \"annoying yt 404 behavior\" in the .md\n                const noAvailablePoster = e.target.naturalHeight == 90 && e.target.naturalWidth == 120;\n                if (noAvailablePoster) return;\n\n                this.style.backgroundImage = `url(\"${webpUrl}\")`;\n            }\n        }, 100);\n    }\n}\n// Register custom element\ncustomElements.define('lite-youtube', LiteYTEmbed);\n"], "mappings": ";AAYA,IAAM,cAAN,MAAM,qBAAoB,YAAY;AAAA,EAClC,oBAAoB;AAChB,SAAK,UAAU,KAAK,aAAa,SAAS;AAE1C,QAAI,YAAY,KAAK,cAAc,cAAc;AAEjD,SAAK,YAAa,aAAa,UAAU,YAAY,KAAK,KAAM,KAAK,aAAa,WAAW,KAAK;AAElG,SAAK,QAAQ,QAAQ,KAAK,aAAa,OAAO,KAAK;AAOnD,QAAI,CAAC,KAAK,MAAM,iBAAiB;AAC/B,WAAK,MAAM,kBAAkB,+BAA+B,KAAK,OAAO;AACxE,WAAK,mBAAmB;AAAA,IAC1B;AAGA,QAAI,CAAC,WAAW;AACZ,kBAAY,SAAS,cAAc,QAAQ;AAC3C,gBAAU,OAAO;AACjB,gBAAU,UAAU,IAAI,aAAa;AACrC,WAAK,OAAO,SAAS;AAAA,IACzB;AACA,QAAI,CAAC,UAAU,aAAa;AACxB,YAAM,iBAAiB,SAAS,cAAc,MAAM;AACpD,qBAAe,YAAY;AAC3B,qBAAe,cAAc,KAAK;AAClC,gBAAU,OAAO,cAAc;AAAA,IACnC;AAEA,SAAK,kBAAkB;AAGvB,QAAG,UAAU,aAAa,KAAI;AAC1B,gBAAU,gBAAgB,MAAM;AAChC,gBAAU,aAAa,YAAY,GAAG;AACtC,gBAAU,aAAa,QAAQ,QAAQ;AAEvC,gBAAU,iBAAiB,WAAW,OAAK;AACvC,YAAI,EAAE,QAAQ,WAAW,EAAE,QAAQ,KAAK;AACpC,YAAE,eAAe;AACjB,eAAK,SAAS;AAAA,QAClB;AAAA,MACJ,CAAC;AAAA,IACL;AAGA,SAAK,iBAAiB,eAAe,aAAY,iBAAiB,EAAC,MAAM,KAAI,CAAC;AAC9E,SAAK,iBAAiB,WAAW,aAAY,iBAAiB,EAAC,MAAM,KAAI,CAAC;AAK1E,SAAK,iBAAiB,SAAS,KAAK,QAAQ;AAM5C,SAAK,aAAa,KAAK,aAAa,QAAQ,KAAK,UAAU,OAAO,SAAS,OAAO,KAAK,UAAU,UAAU,SAAS,MAAM;AAAA,EAC9H;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,YAAY,MAAM,KAAK,IAAI;AAC9B,UAAM,SAAS,SAAS,cAAc,MAAM;AAC5C,WAAO,MAAM;AACb,WAAO,OAAO;AACd,QAAI,IAAI;AACJ,aAAO,KAAK;AAAA,IAChB;AACA,aAAS,KAAK,OAAO,MAAM;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,OAAO,kBAAkB;AACrB,QAAI,aAAY,aAAc;AAG9B,iBAAY,YAAY,cAAc,kCAAkC;AAExE,iBAAY,YAAY,cAAc,wBAAwB;AAG9D,iBAAY,YAAY,cAAc,qCAAqC;AAC3E,iBAAY,YAAY,cAAc,gCAAgC;AAEtE,iBAAY,eAAe;AAAA,EAC/B;AAAA,EAEA,mBAAmB;AACf,QAAI,OAAO,MAAO,OAAO,MAAM,OAAO,GAAG,OAAS;AAElD,SAAK,eAAe,IAAI,QAAQ,CAAC,KAAK,QAAQ;AAC1C,UAAI,KAAK,SAAS,cAAc,QAAQ;AACxC,SAAG,MAAM;AACT,SAAG,QAAQ;AACX,SAAG,SAAS,OAAK;AACb,WAAG,MAAM,GAAG;AAAA,MAChB;AACA,SAAG,UAAU;AACb,WAAK,OAAO,EAAE;AAAA,IAClB,CAAC;AAAA,EACL;AAAA;AAAA,EAGA,MAAM,cAAc;AAChB,QAAG,CAAC,KAAK,eAAe;AACpB,YAAM,KAAK,SAAS;AAAA,IACxB;AAEA,WAAO,KAAK;AAAA,EAChB;AAAA,EAEA,MAAM,oBAAoB;AACtB,SAAK,iBAAiB;AACtB,UAAM,KAAK;AAEX,UAAM,qBAAqB,SAAS,cAAc,KAAK;AACvD,SAAK,OAAO,kBAAkB;AAE9B,UAAM,YAAY,OAAO,YAAY,KAAK,UAAU,EAAE,QAAQ,CAAC;AAE/D,SAAK,gBAAgB,IAAI,QAAQ,aAAW;AACxC,UAAI,SAAS,IAAI,GAAG,OAAO,oBAAoB;AAAA,QAC3C,OAAO;AAAA,QACP,SAAS,KAAK;AAAA,QACd,YAAY;AAAA,QACZ,QAAQ;AAAA,UACJ,WAAW,WAAS;AAChB,kBAAM,OAAO,UAAU;AACvB,oBAAQ,MAAM;AAAA,UAClB;AAAA,QACJ;AAAA,MACJ,CAAC;AAAA,IACL,CAAC;AAAA,EACL;AAAA;AAAA,EAGA,oBAAoB;AAChB,UAAM,WAAW,KAAK,kBAAkB;AACxC,UAAM,aAAa,SAAS,cAAc,UAAU;AAEpD,eAAW,YAAY,SAAS;AAChC,SAAK,OAAO,UAAU;AAAA,EAC1B;AAAA,EAEA,YAAY;AACR,UAAM,SAAS,IAAI,gBAAgB,KAAK,aAAa,QAAQ,KAAK,CAAC,CAAC;AACpE,WAAO,OAAO,YAAY,GAAG;AAC7B,WAAO,OAAO,eAAe,GAAG;AAChC,WAAO;AAAA,EACX;AAAA,EAEA,MAAM,WAAU;AACZ,QAAI,KAAK,UAAU,SAAS,eAAe,EAAG;AAC9C,SAAK,UAAU,IAAI,eAAe;AAElC,QAAI,KAAK,YAAY;AACjB,aAAO,KAAK,kBAAkB,KAAK,UAAU,CAAC;AAAA,IAClD;AAEA,UAAM,WAAW,KAAK,kBAAkB;AACxC,SAAK,OAAO,QAAQ;AAGpB,aAAS,MAAM;AAAA,EACnB;AAAA,EAEA,oBAAmB;AACf,UAAM,WAAW,SAAS,cAAc,QAAQ;AAChD,aAAS,QAAQ;AACjB,aAAS,SAAS;AAElB,aAAS,QAAQ,KAAK;AACtB,aAAS,QAAQ;AACjB,aAAS,kBAAkB;AAG3B,aAAS,MAAM,0CAA0C,mBAAmB,KAAK,OAAO,CAAC,IAAI,KAAK,UAAU,EAAE,SAAS,CAAC;AACxH,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB;AAEjB,eAAW,MAAM;AACb,YAAM,UAAU,+BAA+B,KAAK,OAAO;AAC3D,YAAM,MAAM,IAAI,MAAM;AACtB,UAAI,gBAAgB;AACpB,UAAI,iBAAiB;AACrB,UAAI,MAAM;AACV,UAAI,SAAS,OAAK;AAId,cAAM,oBAAoB,EAAE,OAAO,iBAAiB,MAAM,EAAE,OAAO,gBAAgB;AACnF,YAAI,kBAAmB;AAEvB,aAAK,MAAM,kBAAkB,QAAQ,OAAO;AAAA,MAChD;AAAA,IACJ,GAAG,GAAG;AAAA,EACV;AACJ;AAEA,eAAe,OAAO,gBAAgB,WAAW;", "names": []}