---
title: 在字节工作三年的同时仍保持一定程度的心理健康其实也并非完全不可能
tags: ["日常","工作", "年度总结"]
lang: zh
published: 2024-09-28T16:26:00+08:00
abbrlink: daily/work-for-3-years
description: "在字节工作三年，时间的流逝仿佛在无形中加速。尽管身处快速变化的环境，仍能找到一丝心理平衡。回想起刚入职时的懵懂，那个年轻的自己在杭州八方城的阳台上，眺望着远方的山脉，心中对未来充满期待与好奇。随着时间的推移，职场的挑战与成长交织，形成了一段独特而丰富的经历，让人学会在忙碌中寻找属于自己的节奏。"
---
![标题源](https://blog-img.shinya.click/1726828874334.jpg)

晚饭时间，同事聊起了他买的车，忽然感叹了一句：“感觉工作之后时间变得特别快”

我：“不好说，今天（周五）下午的时间就挺难熬”

虽然很不愿承认，总把自己当学生来看（暂时是组里年龄最小的），但是距我离开校园，满打满算也有三年多。这三年时间算得动荡，但也相对安稳

字节内有句老话：“字节一年，人间三年”，这“九年”时光，也尽在这里了

### 懵懂

如果不算上实习，我是在 21 年 6 月底入职了杭州字节

请看 VCR：[入职一周小记](https://www.nowcoder.com/share/jump/67966291840886568)

杭州字节租下了八方城的两栋楼，却不是整栋，两幢楼基本都只租了最顶上的五六层，风景倒是不错，每天趴在阳台上可以远眺远处的 EFC（余杭最像城里的地方）和更远处的余杭南部的山区，山雨欲来和雨过天晴时的风景算得一绝

我入职的是抖音直播部门下面的某个 ToC 的组，由于提前实习了两个月，帮助建设过新人文档，对组里的人员业务还算了解。但由于实习结束，回校办理毕业适宜耽搁一个月，再入职时，忽然没有需求分配给我了。于是度过了快乐的摸鱼时间，享受了最后一个月的大小周双倍工资

忽然有一天 leader 和 mentor 找到我，问我是否愿意去志愿下北京的一个业务。原来是一个 ToB 平台，和我们同属一个 +2，发展迅速，人手短缺，向 +2 四处要人，摊派到我们组就是一个名额。彼时我刚刚毕业，浑身有使不完的冲劲儿，又恰好闲着，自然满嘴答应

于是就一直跟着北京的那个组做需求，这算是一个向上管理的平台，平台 QPS 很低，但是主要用来产出数据向上汇报，和 ToC 业务的关注点完全不同，重业务轻性能，更早期甚至对一致性也没很高的要求，调用失败了，加个报警，人工看看，影响不大甚至可以不修，再加上平台用户操作频率也不高，日常维护也算的上清闲

我加入时期，恰逢平台大版本迭代，北京组里派了一个研发远程带着我做需求，最初都是写小需求，边边角角，修修改改，主要是熟悉熟悉流程

> [!TIP]
> 需求初评 —— 需求详评 —— （研发开始介入）技术评审 —— 开发 —— （测试开始介入）showcase —— 测试 —— Launch Review —— 上线

有条不紊，一个需求接着一个需求，没有多余的事情。我从最开始的，技术评审都不知道说些什么，甚至专门找个会议室正襟危坐，到后来……

但其实并没有什么后来的进步，只是习惯或者麻木了而已，也逐渐看轻了这些事情。一个线下 bug 也并不会怎么样，就算是线上 bug，大不了修数据

本身用 Golang 写业务就相当 Freelance，再加上需求的快速迭代，没什么时间对代码进行重构，能跑起来就是成功，仓库代码愈发狂乱。许多奇奇怪怪的复杂需求，再加上四处借调导致技术风格不统一，让这个平台逐渐成了一个“大泥球”

但和我又有什么关系呢，我只是一个写小需求的 noob 罢了

由于刚入职，干啥都是新奇的，我甚至周末会到公司主动加班 —— 反正一个人在家也没什么事做，何乐而不为呢。后期给自己找了点事情，完成了 [MYDB](https://github.com/CN-GuoZiyang/Mydb) 和相关的 [教程](/projects/mydb/mydb0)，虽说最后 BUG 不少，也没怎么充分测试，但确实令我干劲十足：八九点下班，能一直写到凌晨一两点，还能再洗个澡打打游戏喝点小酒，三四点睡觉

也结交了不少有趣的人，拉了个小群，每天不工作的时候就在群里吹吹水。这时的字节简直满足了我对互联网的一切想象：自由自在，管理扁平，请个假不用费尽心思编理由，工作电脑也想装啥装啥，甚至因为疫情还没有结束，隔三岔五就居家办公，没有打卡和强制工时，十一点多到公司，吃完晚饭就跑路……

### 发愤

就这么一路开开心心地干着，到了 22 年初。在这期间，我也由一些边角需求开始，逐渐能 cover 住整个领域

阿里有句古话，经常在安慰被裁人员或者被裁时自我安慰时使用：“拥抱变化”

> 唯一不变的，就是变化

平台发展迅猛，业务从直播扩展到了整个抖音，同时由于产品的调整，原本的北京开发团队把平台交接了出去，接盘的恰好是我原本的组

于是便是交接流程，每天的活动就是带着新人（新组的人）做需求，以及各种分享。我在那时已经负责预算模块，于是也组织了一次分享。虽然全程念文档毫无感情，但也确实是我在公司的第一次分享（转正不需要答辩）

随着北京的人逐渐撤出，人力猛然出现短缺。于是在预算模块外，整个项目领域又猛地塞到我手上。恰逢产品提出了一个大需求，我在项目领域的第一个需求，就是将整个项目领域近乎重写了一番

需求撞上春节假期，需求紧急，整个假期除了三十和初一，都在图书馆搓代码，前后由于理解误差等原因（还是太菜了），整个代码被来来回回实现了三四遍，最后终于勉强赶上了节后第一班上线

后续又有各种历史帐清算，各种重构评审，代码迁移到新服务（BTW，2022 年启动的新服务迁移，至今没有迁移完成），忙到脚不沾地

那段时间极其焦虑，加班非常猛。日常处理各种问题都够忙到十几点，基本没有十点之前下过班。对应的就是报复性熬夜和报复性娱乐。典型娱乐比如周五晚上到凌晨在公司找个会议室喝酒，喝到周六大清早回家睡觉 😅

非常神奇的是，每次加班时，都能遇到我组组长，每次十点十一点钟，不是我喊他下班就是他喊我下班。我图上下班方便，直接搬到了公司所在的小区，每天上下班走地下车库；他住的地方每天通勤要五十分钟一小时

可能这就是有家庭的代价吧

由于下班太晚，各种日常开发和学习也基本都停掉了，回到家只想喝酒和睡觉。喝的酒也逐渐从自己调制的鸡尾酒变为伏特加/威士忌直接喝，喝酒的目的也从好喝/微醺变成了助眠/去除焦虑

### 转变

> 有时候宁愿生活一成不变，因为熟悉的平淡胜过未知的风险

风险来的很快

22 年中，我们组换了一个新的 +2，气氛开始微妙变化。原本随意的非正式会议变得正式化，需求流程也变得复杂，简单的步骤被新增了无数的审查环节，每一个决策都需要经过层层把关

各种名词被生造了出来：bug 比人天、人效比、代码复杂度等等。指标的或高或低，通常意味着你在下次周会或者双周会上会被拎出来批斗。产研测的关系也不再和谐，各种会议上的唇枪舌剑、钩心斗角、推诿甩锅，让人精疲力竭，专心写代码成了一种奢望。做多错多，不做不错，能推则推，成了保命技巧。原本“线上 bug 大不了修数据”，到现在“一个回归 bug 就能宣判你的死刑”

也是从这段时间开始，hc 的收缩导致组里开始减员，很多我熟悉的人逐渐要么主动要么被动地离开了，原本每天一起吃饭一起摸鱼一起下班的同事，好些一周才能见到一次，工作的孤独感不断加剧

<img src="https://blog-img.shinya.click/2024/630b780569e7aa8fa40e1ecc6a189b40.png" style="width: 50%"/>

在这种情况下，不知是情绪躯体化，还是这几年熬夜 + 喝酒，或者兼具有之，身体开始报警，具体可见：[慢性胃炎治疗之路](/daily/anti-chronic-gastritis)

体重急剧减轻之下（一个月 12 斤），痛定思痛，决定~~开摆~~开启养生模式！

于是过上了每天八点半起床去公司吃早餐，晚上六点多吃完晚饭直接下班，下班后跑步半小时，十二点前睡觉（目前还在努力）的健康生活

> [!CAUTION]
> 奋斗？命都要没了还怎么奋斗！

好在，前面那段剧变也搞走了不少产品，需求量急剧减少。虽然上面也开启了“没法整活那就整人”模式，又增加了许多乱七八糟的卡点和规范，且现在的 +2 阿里味儿特别浓厚，很喜欢搞一些莫名其妙的团建和个人 show 表演（算是另一种的折磨人）。平台进入低频维护期，琐碎的事情也不再多

外面行情很差，先这么混着吧

### 后记

原本这篇三周年总结应该在 5、6 月份完成，但那时恰逢产品剧变 + 身体不适，这篇文档的标题就躺在我的备忘录三个月，直到最近身体状况稳定了下来，才又拾起了总结。然而就是这三个月，我的心态就从积极进取变成了“先这么混着”，不禁让人感叹世事多变

但是好在，一定程度的心理健康也确实保持住了。最近在研究心理状态检测 + 日程规划，尝试做一个自律的人，虽然暂时的成果是能直观的看到时间被浪费到哪了，后续会写篇文章介绍下这方面的成果

这些年出国留学的心思还是在蠢蠢欲动，尤其是遇到各种不如意时，就尤其上头。在无数忙碌了一天后的深夜，这个念头便犹如藤蔓，爬满心头。这三年距离我辞职最近的时刻，就是我在医院等待胃镜时，还在回复飞书消息处理 oncall 的时刻。我时常问自己：“为了这些工资，付出的这些代价真的值得吗”。但实际冷静下来，还是叹了口气，狠心摁下了这想法

不知道再过三年，我是否会后悔甚至痛恨现在的自己。但这也许就是选择的诅咒

> 无论你如何选择，甚至不做选择，你都必须承受代价