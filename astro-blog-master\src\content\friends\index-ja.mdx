---
lang: 'ja'
---
import FriendCard from '@/components/Widgets/FriendCard.astro';

<FriendCard 
  name="<PERSON>" 
  url="https://oliverchen12.github.io" 
  avatar="https://blog-img.shinya.click/2024/5e107c1356d8cd90f1b98d170368c2df.jpg" 
  description="互联网新星" 
/>

<FriendCard 
  name="Sun Yushuo" 
  url="https://yyd-piren.github.io" 
  avatar="https://blog-img.shinya.click/2024/a63e3e016fdaf653fde08969916830eb.JPG" 
  description="风雨湿征衣" 
/>

<FriendCard 
  name="子行" 
  url="https://www.cnblogs.com/fallingdust" 
  avatar="https://blog-img.shinya.click/2024/9387729ab1b3f7f1c9a7c644d306c851.PNG" 
  description="往日痕迹" 
/>

<FriendCard 
  name="Ancy" 
  url="https://anxcye.com" 
  avatar="https://avatars.githubusercontent.com/u/91717732" 
  description="Coding with love!"
/>

<FriendCard 
  name="Zwei" 
  url="https://zwei.de.eu.org" 
  avatar="https://avatars.githubusercontent.com/u/110226580?v=4" 
  description="人生如白驹过隙，历史似长河永流"
/>

---

## 相互リンク申請

もし当ブログと相互リンクをご希望の場合は、下記の条件を満たしていることをご確認ください。
1. サイト内容が健全で、前向きであること
2. サイトが正常にアクセスできること
3. 一定量のオリジナルコンテンツがあること

当ブログ情報
```
title: 信也のブログ
url: https://shinya.click
description: コーダー
avatar: https://shinya.click/logo.png
```

まず、貴サイトに当ブログをリンクしていただき、以下の方法でご連絡ください。
- [Email](mailto:<EMAIL>)
- コメント欄でのメッセージ
- Github issue
::github{repo="senshinya/astro-blog"}

<br />
申請時にご提供いただきたい情報：
- サイト名
- サイト URL
- サイトの説明（簡単な紹介）
- アイコン／ロゴの URL
