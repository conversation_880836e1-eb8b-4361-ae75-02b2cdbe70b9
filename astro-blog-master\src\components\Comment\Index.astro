---
// import Disqus from '@/components/Comment/Disqus.astro'
import Giscus from '@/components/Comment/Giscus.astro'
import Twikoo from '@/components/Comment/Twikoo.astro'
import Waline from '@/components/Comment/Waline.astro'
import { themeConfig } from '@/config'

const enableComments = themeConfig.comment.enabled

// Disqus
// const disqusShortname = themeConfig.comment?.disqus?.shortname ?? ''
// const showDisqus = enableComments && Boolean(disqusShortname.trim())

// Giscus
const giscusRepo = themeConfig.comment?.giscus?.repo ?? ''
const showGiscus = enableComments && Boolean(giscusRepo.trim())

// Twikoo
const twikooEnvId = themeConfig.comment?.twikoo?.envId ?? ''
const showTwikoo = enableComments && Boolean(twikooEnvId.trim())

// Waline
const walineURL = themeConfig.comment?.waline?.serverURL ?? ''
const showWaline = enableComments && Boolean(walineURL.trim())
---

<!-- {showDisqus && <Disqus />} -->
{showGiscus && <Giscus />}
{showTwikoo && <Twikoo />}
{showWaline && <Waline />}
