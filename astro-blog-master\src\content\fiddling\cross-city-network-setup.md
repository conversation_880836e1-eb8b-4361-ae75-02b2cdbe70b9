---
title: 上海杭州异地组网记录
lang: zh
published: 2025-04-18T16:43:12.812+08:00
tags: ["折腾","透明代理","软路由","组网","mihomo","tailscale"]
abbrlink: fiddling/cross-city-network-setup
description: "随着工作调动，npy 从北京迁至上海，顺便处理了宽带安装。尽管选择了电信500M的宽带，费用却高于杭州的1000M，令人无奈。在此背景下，我决定构建一个跨城市的网络环境，期望实现上海透明代理、部分流量从杭州出口及两地局域网互通的目标。杭州的网络架构相对简单，通过软路由与无线AP的组合，搭建了一套可以支持日常流量回家的系统，准备迎接接下来的网络挑战。"
---
### 前言

这个月 npy 因为工作原因，从北京搬到了上海

帮忙搬家之外，还帮着开了一条宽带，电信 500M

顺便吐槽下上海电信，500M 的宽带比杭州电信 1000M 宽带还贵

~~你这光纤是金子做的还是光猫是金子做的？~~

作为一名 Geek（自封），不折腾是不可能的，合计了一下，预期要达成以下成果

1. 上海透明代理越墙
2. 上海部分流量从杭州出口
3. 两地局域网互访

第二条的原因是目前 Netflix 等流媒体解锁是走的机场（此处可插入 aff），这个机场限制了使用用户，多地同时使用可能会导致封号，这种情况下就只能将流量转发到杭州，从杭州统一出口

### 前置准备

目前杭州的网络拓扑很简单，光猫拖一个软路由拨号，软路由接一个 TP-Link 做无线 AP。同时有个小主机直连软路由，开了一个 shadowsocks server 用于日常流量回家。软路由是倍控的 G30S（此处可插入广告），N5100 也是够用的，系统是 ImmortalWRT。装了以下插件

* AdguardHome：DNS 去广告
* Nikki：Mihomo 内核，做透明代理
* Tailscale：虚拟局域网组网，此前一直用作流量回家的备案
* DDNS-Go：如你所见，就是个 DDNS

LAN 网段为 ***********/24

为了保持设备一致，降低一点复杂度，于是又购置了一台相同配置的 G30S，也安装了 ImmortalWRT。同时上海的网络拓扑计划与杭州保持一致。光猫 - 软路由 - AP

### 安装

ImmortalWRT 的安装得提一嘴，最初以为它的安装和 windows 或者其他 linux 发行版类似，镜像是个 livecd 或者安装器之类的，写入 u 盘启动后执行安装。没想到 u 盘启动后竟然就这么启动了……

启动了……

合着那个 img 直接就是系统镜像啊

于是麻溜改刷 winpe，用 physdiskwrite 把镜像写到硬盘，成功启动，简单配置完成了上网和 dhcp。上海的 LAN  网段为 ************/24

接着就是扩容，直接写盘安装的 openwrt，可用空间只有不到 1G，硬盘的其他剩余空间完全浪费掉了。这时候第二个困难就出现了：我找不到何时的扩容教程。简中的教程错综复杂、错误百出、一个抄一个，但实际上，要么就是 squashfs 的扩容，要么就是用剩余空间新建一个分区，将根分区改挂到新分区上这样的教程，几乎找不到真正的“扩容”分区

最后在 OpenWRT 的 [官方文档](https://openwrt.org/docs/guide-user/advanced/expand_root) 中找到了……以后还是不用中文搜索了，Google 都搜不出啥有用的

先解决越墙和流量出口的问题，这个直接 clash 就能解决

[Nikki](https://github.com/nikkinikki-org/OpenWrt-nikki) 是一个 OpenWRT 插件，基于 Mihomo 内核进行透明代理。相对于其他类似功能的插件如 Passwall 或者 clash，Nikki 的可定制化更强。注意安装要根据 Readme 中的 Instruction 安装，直接从 Releases 下载 ipk 是无法安装的（很奇怪）

::github{repo="nikkinikki-org/OpenWrt-nikki"}

安装完成后导入配置文件，就可以启动了。有几个注意点：

1. 可以直接开启 tun 模式，会自动配置路由器的路由表做透明代理，基本没什么问题
2. mihomo 的域名分流需要 dns 请求通过 mihomo 发起，这时有两个选择：mihomo 的 dns 直接 hijack 53 端口，openwrt 自带的 dnsmasq 可以改为监听其他端口；或者 dnsmasq 设置一个转发，将 dns 请求都转发到 mihomo 的 dns 端口
3. 如果使用 FakeIP 模式，注意仔细设置 FakeIP Filter

其他就是一些老生长谈，和其他平台使用 mihomo 基本一致，不再赘述

配置完成后开启 Nikki，在 Dashboard 中就能看到 mihomo 开始接管路由器的所有出口流量了

配置文件基本是直接从我这儿粘过去的，大部分越墙流量都是向搬瓦工🪜直接发起连接，一些需要流量从杭州出口的规则，outbound 都改为了杭州的 shadowsocks server，试了下延迟还可以，就当是走了一道国内中转

到这一步，其实通过域名的话已经可以实现上海访问杭州的内网，由于是 FakeIP 模式，dns 解析后返回的 fakeip 请求也会被 mihomo 内核处理，再通过域名规则将流量转到杭州即可。但是这样的话，直接的 ip 请求，不通过 dns，就无法通过 mihomo 转发（tun 默认不接管局域网请求），且杭州也没法访问到上海

于是，TailScale 堂堂登场！

OpenWRT 使用 TailScale 可以用 [luci-app-tailscale](https://github.com/asvow/luci-app-tailscale) 插件，可以比较直观地管理和配置 tailscale

::github{repo="asvow/luci-app-tailscale"}

安装完成后首先启动一下，完成登录认证，再进行配置，最好在 tailscale 关闭这个设备的密钥过期

高级设置中

* 勾选启用路由：tailscale 自动帮我们配置到其他子网的路由规则
* 不勾选允许 dns：只需要路由 ip 请求即可
* 公开网段填写 ************/24：告诉 tailscale 这台设备可以进行这个网段的路由
* 勾选子网互通：字面意思
* 子网路由选择 ***********/24：表示这个子网范围的路由由 tailscale 接管

注意，杭州的 tailscale 也要做对应的设置，注意公开网段和子网路由要用对应的网段

设置完成后重启一下 tailscale，就可以完成子网互通了。在上海和杭州都可以直接通过对方网段的 IP 直接访问对方网段的主机

### 后记

上海的宽带安装的是一个什么云宽带，关闭云宽带并且开启桥接的流程非常麻烦（至今没走完）

所以以上配置都是在上海的软路由没有公网 IP 的情况下进行的

好在由于杭州这边有公网 IP，tailscale 也是可以直接打洞成功，延时只有十几毫秒。如果两边都没有公网 IP，那就只能自求多福了，要么就自建 derp 转发，要么就只能顶着大几百的延时凑活着用了

还是想吐槽下上海电信，开个桥接又要签协议又要拍照又要审核的，和防贼似的
