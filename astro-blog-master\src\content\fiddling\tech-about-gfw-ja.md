---
title: GFWの原理考察
tags: ["試行錯誤","ファイアウォール","国際インターネット","VPN"]
lang: ja
published: 2024-06-23T15:31:32+08:00
abbrlink: fiddling/tech-about-gfw
description: "GFWの動作メカニズムは単なる出口ゲートウェイの監視ではなく、バイパス監視技術によって国際トラフィックを検査しています。この方法により、すべての出入国IPパケットがGFWクラスターに複製され、深い分析とフィルタリングが行われます。この点を理解することは、VPN 方法の研究に不可欠であり、検閲の具体的な経路と技術手段を明らかにします。GFWのネットワークトポロジーを深く探ることは、より効果的にネットワーク検閲に対処し回避する助けとなります。"
---
技術に罪はない。

> ファイアウォール（英語：Great Firewall、GFW）、中国国家ファイアウォール、または単に「壁」や「ファイアウォール」とも呼ばれ、中国ネット情報弁公室はこれを「データ越境セキュリティゲートウェイ」と称し、同国政府が国際インターネットの出口コンテンツをフィルタリングするためのハードウェアおよびソフトウェアシステムの集合体である。———— Wikipedia

GFW に効果的に対抗するには、どのウェブサイトがブロックされているかだけに注目するのではなく、そのブロックメカニズムを深く理解すべきです。Google がブロックされていることを知っても直接 VPN 突破に役立つわけではありませんが、GFW が Google をどのようにブロックしているかを理解することは、VPN 方法の選択と実装にとって極めて重要です。したがって、VPN 方法を議論する前に、まず GFW のブロック原理を深く研究しなければなりません。

### GFW はどこにあるのか

多くの人は当然のように考えるかもしれません：GFW は当然出口ゲートウェイに配置されており、そこで全ての出口トラフィックを直接キャプチャして検査していると。しかし実際には、gfwrev.blogspot.com の検証によると、GFW は「3 つの国際出口でバイパス監視を行っている」とされ、スプリッティング技術を用いてすべての出入国 IP パケットを GFW クラスターに複製し検査しています。推測される GFW のネットワークトポロジーは以下の通りです（画像出典：gfwrev.blogspot.com）：

![gfw topology](https://blog-img.shinya.click/2025/4beb5462a598c9015c56c75fa73ae301.svg)

GFW は異なる回線のリンク異種性を結合し、多様なリンク結合技術を研究しています（出典：[高速ネットワーク環境下の侵入検知システム構造研究](https://xueshu.baidu.com/usercenter/paper/show?paperid=f46cb7e5a6dbf7b9cb81b1dd3b9965ce)）。『国際通信出入口局管理規則』によれば、主要 ISP は共用の国際光ケーブルで合流し、安管センター（CNNISC）は独立した交換センターを持ち、各 ISP はそれぞれの交換センターに接続しています。異なる ISP のリンク仕様に対応するため、GFW の交換センターは異なるリンクを統合し、各 ISP からバイパス接続を引き出しています。接続回線は主に光ファイバーであるため「バイパススプリッティング」と呼ばれます。実験により、GFW の接続地点は必ずしも最終ホップに近いわけではなく、図中では破線で示されています。

これらに関しては、より厳密な研究論文があります：[Internet Censorship in China: Where Does the Filtering Occur?](https://web.eecs.umich.edu/~zmao/Papers/china-censorship-pam11.pdf)

初期（2010 年）の研究では、GFW プロジェクトは「仮想計算環境実験床」計画として偽装されていると考えられています。

> 「仮想計算環境実験床」は国家コンピュータネットワーク緊急技術処理調整センター（CNCERT/CC）とハルビン工業大学（HIT）が協力して構築し、全国 31 省のネットワーク基盤と計算資源を統合・活用し、大規模でオープンかつ安全、動的で制御可能な仮想計算環境実験プラットフォームを構築し、仮想計算環境の集約と協調メカニズムを研究・検証するものです。

「仮想計算環境実験床」公開論文：[計算グリッド環境下における多アドレス協調型ジョブレベルタスクスケジューリングアルゴリズム](https://dds.sciengine.com/cfs/files/pdfs/1674-5973/LcNrPPSfzafrc3Pmn.pdf) によると、2005 年のプラットフォーム構成は以下の通りです：

| サイト | 地理的位置 | 機種 | ノード数 | 各ノードのプロセッサ | 各ノードのメモリ |
|  ----  | ----  |  ----  | ----  |  ----  | ----  |
|CNCERT/CC|北京 | 曙光 4000L|128 ノード|2*Xeon 2.4G|RAM2G|
|HIT|ハルビン | 曙光サーバー|32 ノード|2*Xeon 2.4G|RAM2G|
|CNCERT/CC|上海|Beowulf クラスター|64 ノード|2*AMD Athlon 1.5G|RAM2G|

もちろん、これは 2005 年の構成であり、現在の GFW のハードウェア・ソフトウェア構成は容易に特定できません。

### データ処理

GFW は IP パケットを取得後、あなたとサーバー間の通信を継続させるかどうかを決定します。過度に攻撃的であってはならず、全国的に海外サイトへのアクセスを遮断することはその存在意義に反します。GFW は IP パケットの意味を理解した上で、あなたと海外サーバー間の接続を安全に遮断するか判断します。まず TCP プロトコルを再構築し、最終的に完全なバイトストリームを復元し、その上で HTTP などの具体的なアプリケーションプロトコルを分析します。アプリケーションプロトコル内に不適切な内容があるか調べ、対応方法を決定します。

議論を簡単にするため、以下の 3 つの TCP パケットを仮定します：

```
IP パケット 1：TCP パケットを含み、データ：Get /inde
IP パケット 2：TCP パケットを含み、データ：x.html H
IP パケット 3：TCP パケットを含み、データ：TTP/1.1
```

再構築は、IP パケット 1 の「GET /inde」と IP パケット 2 の「x.html H」、IP パケット 3 の「TTP/1.1」を結合し、「GET /index.html HTTP/1.1」にすることです。結合されたデータはテキストか暗号化されたバイナリプロトコルかもしれません。具体的にはあなたとサーバー間で約束されたものです。GFWは盗聴者として推測しなければ内容を知りません。HTTPプロトコルは標準化されており暗号化されていないため、GFWは再構築後に簡単にHTTPを使っていることやアクセス先サイトを把握できます。

このようなバイトストリームの再構築で難しいのは膨大なトラフィックの処理です。この問題は [このブログ](http://gfwrev.blogspot.tw/2010/02/gfw.html) で明確に説明されています。原理はウェブサイトのロードバランサーと同様です。特定の送信元と宛先に対してハッシュアルゴリズムでノードを決定し、そのノードに関連するトラフィックを全て送ることで、一つのノードで TCP セッションの単方向バイトストリームを再構築できます。

最後に議論を完結させるために 2 点述べます：

1. GFW の再構築はバイパスのスプリッティングによるものですが、GFW の全ての機器がバイパス上にあるわけではありません。後述のように、一部の GFW 機器はバックボーンルーター上に配置され、Google の HTTPS の断続的なパケットロスなど、GFW が一部の IP ルーティングに関与していることを示しています。
2. 再構築は単方向の TCP ストリームであり、GFW は双方向の会話内容には関心がなく、片方向の内容だけで判断します。しかし監視自体は双方向であり、国内から国外、国外から国内のトラフィックは両方再構築され分析されます。つまり一つの TCP 接続は GFW により 2 つのバイトストリームに分けられます。

### 分析

分析は GFW がバイトストリームを再構築した後に行う第二段階です。再構築では主に IP、TCP、UDP プロトコルを処理しますが、分析では多種多様なアプリケーション層の複雑なプロトコルを理解する必要があります。場合によっては新しいプロトコルを独自に発明することも可能です。

総じて、GFW のプロトコル分析には似て非なる 2 つの目的があります。第一は不適切な内容の拡散防止、例えば Google 検索で「してはいけない」キーワードを検索することの阻止。第二は VPN などの検閲回避ツールの利用防止です。

GFW が第一の目的を達成するために、HTTP や DNS などのプロトコルの平文検査を行います。大まかな方法は以下の通りです：

```
1. 特徴検出
2. パケット分解
3. キーワードマッチング
```

HTTP のようなプロトコルは明確な特徴があるため、最初のステップは問題ありません。GFW は HTTP パケットを検出すると、HTTP のプロトコル規則に従ってパケットを分解します。この分解は GFW がプロトコルを理解して行います。例えば HTTP の GET リクエストから URL を取得します。次に GFW はこの URL をキーワードと照合し、例えば URL に Twitter が含まれているかを調べます。なぜ分解が必要かというと、分解後の検査はより精密で誤検知を防ぎ、全文検索よりもリソースを節約できるためです。また、[liruqi/jjproxy](https://github.com/liruqi/jjproxy) のコアは GFW の HTTP パケット分解の脆弱性に基づいていますが、このバグは既に修正されています。原理は GFW が HTTP パケットを分解する際、余分な\r\nを処理できなかったが、google.com は正しく処理できたというものです。この例から、GFW はまずプロトコルを理解し、その後キーワードマッチングを行っていることが証明されます。キーワードマッチングは効率的な正規表現アルゴリズムを用いていると考えられ、特に議論の余地はありません。

現在知られている GFW のプロトコル分析は以下の通りです：

#### DNS プロトコル

GFW は UDP の 53 番ポートの DNS クエリを分析できます。クエリのドメイン名がキーワードにマッチすると DNS ハイジャックが行われます。これは単なるブラックリストではなく、正規表現に似た仕組みを用いていると断言できます。理由はサブドメインが非常に多いためです。証拠として：

- 2010 年 3 月、チリのドメイン登録技術者が中国のルートサーバーに facebook.com、youtube.com、twitter.com などの問い合わせを行った際、応答が異常であることを発見。中国のルートサーバー運営者 Netnod は一時的に国際インターネット接続を切断。セキュリティ専門家は Netnod の問題ではなく、中国政府がネットワークを改変したことが原因と推測。
- 2014 年 1 月 21 日午後 3 時半、中国のインターネットドメイン解決に異常が発生し、多数のサイトが誤って IP アドレス ***********（米カリフォルニア州フェリモンの Hurricane Electric 社）に解決された。この IP は VPN 接続ノードとして使用されていた。動的ネット社と研究者は GFW スタッフの操作ミスと考え、一部は真のハッカーがこの IP を踏み台に攻撃を仕掛けた可能性も排除できないとした。
- 2015 年 1 月 2 日、汚染方法が変更され、GFW は固定のブロック IP ではなく、海外の実在しアクセス可能なサイトの IP を注入。これにより海外サーバーが中国からの DDoS 攻撃を受け、一部サイトが中国 IP をブロック。4 月に CNCERT はこれが海外攻撃によるものと声明。

出典：https://zh.wikipedia.org/wiki/%E9%98%B2%E7%81%AB%E9%95%BF%E5%9F%8E

#### HTTP プロトコル

GFW は HTTP プロトコルを識別し、GET の URL と HOST を検査します。キーワードにマッチすると TCP RST で接続を遮断します。

#### TLS プロトコル

初期の TLS バージョンでは、サーバーのハンドシェイク応答（証明書含む）は暗号化されておらず、GFW はこれを嗅ぎ取りアクセス先サイトを知ることができました。TLS 1.3 以降は ServerHello 以降のハンドシェイク情報も暗号化され、証明書情報の検出は防止されると一般に考えられています。

しかし現在広く使われる SNI（Server Name Indication）プロトコルは TLS の拡張であり、クライアントがハンドシェイク開始時に接続先ドメイン名をサーバーに伝えます。これは複数の HTTPS サイトを運用するために必要ですが、この拡張は暗号化されていません。GFW は SNI の平文ドメイン名を嗅ぎ取りブロックしています。HTTPS は HTTP＋TLS なので、HTTPS 接続の検査は HTTP 検査に分類されます。

**注意：GFW はターゲットドメインの証明書を取得できないため、HTTPS の実際の内容を解読することはできません。**

#### トラフィック特徴識別

GFW の第二の目的は VPN ソフトウェアの封殺です。これを達成するために GFW はより過激な手段を取ります。理由は簡単で、HTTP プロトコルの封鎖は誤検知がインターネットの正常運用に影響するため、GFW はインターネットと共生関係にあります。だが TOR のようにほぼ純粋に VPN 目的のプロトコルは検出されれば即座に封殺されます。GFW が各種 VPN プロトコルをどのように封鎖しているかは私も詳しく知りませんが、状況は日々変化しています。以下 2 つの例で GFW の高度な技術を示します。

最初の例は GFW による TOR の自動封鎖で、GFW がプロトコル自体を最大限理解しようとする姿勢を示しています。ブログ https://blog.torproject.org/blog/knock-knock-knockin-bridges-doors によると、中国の IP から米国の TOR ブリッジに接続すると GFW に検出されます。15 分後、GFW はクライアントを装い TOR プロトコルでそのブリッジに接続を試みます。ブリッジが TOR であると確認されると、そのポートを封鎖します。ポートを変えればしばらく使えますが再度封鎖されます。これは GFW が国際出口トラフィックから TOR ブリッジを鋭敏に検出できることを示し、TOR のハンドシェイクに特徴的な署名があるためです。もう一つは GFW が自らクライアントを装い接続を試みる労を惜しまない点です。

二つ目の例は GFW が暗号化トラフィックの内容に敏感語があるかどうかは気にせず、VPN 疑いのあるトラフィック、特に商用 VPN サービスを封殺することです。（ほぼ確実に）GFW は暗号化トラフィックの中から VPN サービスを機械的に識別できるレベルに進化しています。

GFW の最近の重点はネットワークトラフィックの分析にあり、VPN トラフィックを識別する研究はまだ少なく、大規模展開は問題を起こしやすいという特徴があります。

### 干渉手段

GFW はプロトコル分析で「脅威のある」バイトストリームと判断すると、以下の方法で通信を妨害します：

#### IP ブロック

一般的に人手による検査後の対応です。GFW の機械的検出で直接 IP を封鎖する方法は聞いたことがありません。通常は GFW が検出し、TCP RST で接続をリセットします。一定期間後に IP が封鎖され、明確な時間規則はありません。推測ですが、全体的な IP 封鎖は人手介入が必要です。全体的な IP 封鎖と、部分的な IP 封鎖（例えば 3 分間だけ特定 IP へのアクセスを封鎖し他はアクセス可能）とは全く異なる方法です。現象は似ていますが、ping で twitter.com を試せば長期間封鎖されていることがわかります。

実装方法は無効ルート（ブラックホール）をバックボーンルーターのルーティングテーブルに追加し、これらのルーターが指定 IP へのパケットを破棄します。ルーティングテーブルは BGP プロトコルで動的に更新されます。GFW は封鎖 IP リストを管理し、BGP で広報するだけです。国内のバックボーンルーターは GFW の一部のように振る舞い、共犯者となります。

traceroute で全体的に封鎖された IP を調べると、パケットは GFW の国際出口に到達する前に電信や聯通のルーターで破棄されていることがわかります。これが BGP 広報の効果です。

#### DNS ハイジャック

これも人手検査後の対応です。不適切なサイトを発見すると、そのドメイン名をハイジャックリストに追加します。原理は DNS と IP プロトコルの弱点に基づき、DNS も IP もサーバーの権威性を検証せず、DNS クライアントは最初に受け取った回答を盲信します。つまり facebook.com を問い合わせると、GFW は正しい回答が返る前に先に偽の回答を送り、DNS サーバーを装います。

#### TCP 接続リセット

TCP プロトコルでは RST パケットを受け取ると接続は即座に切断されます。ブラウザでは接続がリセットされたと表示されます。このエラーは多くの人に馴染みがあるでしょう。私の感覚では、これは GFW の主要な封鎖手段です。多くの RST は条件付きで、URL に特定キーワードが含まれると発生します。facebook など多くのサイトがこの対象です。また無条件に RST されるサイトもあり、特定 IP とポートに対して内容に関係なく RST が送られます。有名な例は HTTPS の Wikipedia です。GFW は IPv4 の弱点を利用し、ネット上で誰にでも成りすましてパケットを送れます。これにより RST が Google から送られたとあなたに信じ込ませ、Google にはあなたが RST を送ったと信じ込ませることが可能です。

#### ポート封鎖

GFW の主体はバックボーンルーターのバイパス上にある侵入検知装置（IDS）で、スプリッティング技術でパケットをキャプチャし侵入検知を行います。加えてこのルーターはポート封鎖（IPS）にも使われます。侵入検知後、GFW は TCP RST で接続を遮断するだけでなく、バックボーンルーターで特定 IP やポートに対してポート封鎖や IP 封鎖、選択的パケット破棄などの封鎖措置を行います。これはルーター上の「iptables」機能に似ており（ネットワーク層とトランスポート層のリアルタイムパケット分解とルールマッチング）、Cisco ルーターでは ACL Based Forwarding（ABF）と呼ばれます。ルールは全国で同期され、一台のルーターがポートを封鎖すれば、全国の GFW 搭載バックボーンルーターも同様に封鎖します。通常このポート封鎖は VPN サーバーに対して行われ、SSH や VPN サービスを検出すると、全国の出口バックボーンルーターに ACL ルールを展開し、そのサーバーの特定ポートからの下りパケットをフィルタリングします。つまり国外から国内へのパケットで、送信元 IP が封鎖されたサーバーIP、送信元ポートが封鎖ポートの場合、そのパケットは破棄されます。このルールの特徴は、上りパケットはサーバーに届くが、下りパケットは破棄されることです。

封鎖されたポートをサーバーが変更して対応しても、すぐに再封鎖されます。複数回試みると IP 封鎖に至ります。推測ですが、ポート封鎖は GFW の自動対応ではなく、ブラックリストと人手によるフィルタリングで実施されているようです。その理由の一つは、封鎖は昼間の勤務時間に多発しているという報告があるためです。

#### 逆壁（リバースファイアウォール）

多くの VPN サービスが国内中継サーバーを使っていますが、GFW は異常な大量の国外トラフィックを検出すると、特に全国会議や国慶節などの特別期間にそのサーバーを逆壁化します。具体的には国外サーバーがその逆壁化された IP にアクセスできなくなります。ping すると国外側は全てタイムアウト（赤）、国内側は正常（緑）となります。対処法は少なく、IP を変更するか、敏感期間が過ぎるのを待つしかありません。

> 本文は以下を転載・編集・補足したものです
> 1. https://ednovas.xyz/2022/06/25/gfw/#%E4%B8%AD%E8%BD%AC
> 2. https://gfwrev.blogspot.com/2010/02/gfw.html