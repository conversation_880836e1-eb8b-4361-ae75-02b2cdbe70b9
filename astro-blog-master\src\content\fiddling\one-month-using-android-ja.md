---
title: Android移行一ヶ月記
lang: ja
published: 2025-06-05T23:26:00.000+08:00
tags: ["試行錯誤","Android","Oppo","アップル","Apple","スマホ"]
abbrlink: fiddling/one-month-using-android
description: "頻繁に機種変更を繰り返してきた私が、一加（OnePlus）からiPhoneへと移行し、最初は試行錯誤の楽しみを味わいながらも次第にエコシステムへの依存へと変わっていった。最近、彼女の勧めでOppo Find X8 Ultraを手に入れ、主に写真撮影の体験向上を目的としている。Appleのエコシステムに浸りつつも、アプリの移行問題に直面し、Androidのアプリ環境がまだまだ不均一であることを痛感。代替アプリ探しに苦労したこの一ヶ月の移行体験は、異なるプラットフォーム間の摩擦と適応を実感させてくれた。"
---
### はじめに

私は機種変更が非常に頻繁な人間で、特に仕事で少し貯蓄ができるようになってからは、「新しいもの好きで古いものを嫌う」という悪い癖がついてしまい、一台のスマホを一年以上使い続けることがほとんどありません。新機種が発売されると、すぐに主力機としてどうか、今使っているソフトやツールチェーンをどう移行するかを考え始め、考えているうちに手がうずいてしまい、つい注文して機種変更してしまいます。旧機種は家族に譲ったり、中古で売ったり、予備機として手元に置いておいたりしますが、やがて忘れ去られていきます。

卒業してからのこの 4 年間で、主力機として使ったのは以下の通りです：一加 8T、一加 9Pro、Pixel 5、Oppo Find X6 Pro、Vivo X Fold3 Pro、iPhone 14 Pro、iPhone 16 Pro。さらに一加 7Pro を予備機として持っています。

機種変更の歴史から、私のスマホに対する心境の変化が見て取れます。初期の一加や Pixel は、卒業したばかりの頃でいじるのが好きで、ほぼ原生に近い状態で root 化や bootloader の解除など自由にカスタマイズしていました。Oppo や Vivo に移ると、徐々にカスタマイズは控えめになりましたが、Vivo の折りたたみ画面のように遊び心は残していました。そして iPhone に至っては、完全に Apple 陣営に入り、Apple のエコシステムに深く溶け込んでいます。

Apple のエコシステムにどっぷり浸かって抜け出せないまさにその時、彼女から痛烈な一言をもらいました。「あなたの写真、ひどすぎるよ」と。確かに、昔はスマホの性能やベンチマークスコアばかり気にしていて、カメラは QR コードが読めれば十分でした。今は彼女に証明したいのです。

> 技術的な不足は、ソフトとハードの工夫で補える！

もちろん、これはまた機種変更の口実に過ぎません。実際、前回の [関西旅行](/ja/travels/kansai-202504) で、K 君のカメラに強く惹かれました。特にあの大きな望遠レンズ。しかし私はもともと面倒くさがりで、後処理をしたくないので、レンズや色調整が少し良いスマホを買って、撮影欲を満たそうと思いました。

ちょうど今年上半期に三大メーカーがそれぞれ Ultra モデルを発表しました：Vivo X200 Ultra、Oppo Find X8 Ultra、Xiaomi 15 Ultra。まず Xiaomi は除外（<del>雷軍！金凡！</del>）、Vivo の望遠ズームは非常に魅力的で、まさにライブコンサートの神器ですが、私はコンサートのチケットが取れないので、結局 1 インチの大型センサーを搭載した Oppo Find X8 Ultra を選びました。現在、機種変更してから一ヶ月が経ちましたが、移行で最も手間取ったのは Apple のエコシステムからの移行でした。特に多くのアプリ開発者が iOS 版のみを開発しており、Android 版のアプリ環境はまだまだ不均一で、代替アプリ探しにかなり時間を費やしました。BootLoader のアンロックができないため、root 化は夢のまた夢で、多くの機能は妥協せざるを得ませんでした。

### 広告の除去

国産 Android エコシステムに乗り換えて最大の問題は、どこにでもある広告です。まず最初にやったことは広告の除去です。

<iframe src="//player.bilibili.com/player.html?isOutside=true&aid=113746622021969&bvid=BV18c6JYLEmw&cid=27626637570&p=1&autoplay=0" scrolling="no" border="0" frameborder="no" framespacing="0" allowfullscreen="true"></iframe>

これを終えた後も、天気アプリの二次画面の下部にある推薦広告だけは消せず、まるで牛皮癣（いぼ痔のようにしつこい広告）です。

### 写真のバックアップ

これまでは Apple のエコシステムをフル活用していたため、すべての写真は iCloud に保存していました。そのため iCloud の 200GB プランを契約し、写真専用に使っています。現在 50GB ほど使用しており、大学時代からのすべての写真が含まれています。先日 NAS を飛牛システムに変更し、これらの写真を飛牛の標準アルバムにも同期バックアップしました。

今回 Android に切り替えるにあたり、ブランド純正のクラウドは使う気がありません（特に歓太云のことです）。Google フォトに移行するのは面倒です。昨年は Google Photos から iCloud に写真を大量に移行しましたが、また逆に戻すのは何のため？さらに Google のクラウド容量は無料で 15GB しかなく、同窓会のメールアドレスで Google AI Pro の 2TB プランを利用していますが、心配は拭えません。

他のデバイスはまだ Apple なので、結局 iCloud ストレージを使い続けることにしました。Mac に O+ 互聯をインストールし、毎日パソコンを開くと自動で接続されます。ただし写真のエクスポートとインポートは手動です。正直、O+ 互聯の使い勝手は V 社のオフィススイートよりかなり劣り、バグも多く機能も不十分です。クリップボード同期はできず、Mac からスマホの操作もできず、写真の自動同期もありません。Oppo 側もあまり更新しておらず、まあ我慢して使っています。

### プロキシ／自宅ネットワークへの接続

スマホとパソコンにはプロキシソフトを入れており、主に自宅ネットワークに接続するために使っています。理由は二つあります：
1. 自宅に設置したサービス（前述のアルバムやノートサービスなど）にアクセスするため
2. 自宅に設置した DNS 広告除去やルールベースの透明プロキシを利用し、複雑な設定なしで自宅と同じネット環境を享受するため

iPhone 時代は Surge を 24 時間バックグラウンドで起動し、全体を自宅経由にしていました。しかし自宅にいるときに全体プロキシをかけたままだとネットに繋がらなくなります。Surge には「サブネットカバー」という重要な機能があり、接続中の Wi-Fi SSID に応じて動作を変えられます。例えば自宅の SSID に接続したら全体プロキシを一時停止するなど。これで自宅でのプロキシ問題は完璧に解決し、Surge はずっとバックグラウンドで動かせました。

Android に切り替えると、まず問題になるのは選択肢が多すぎることです。iOS はほとんどが独自実装のプロキシコアを使っていますが、Android は mihomo（clash）、v2ray、xray、最近の singbox などオープンソースのコアをそのまま使い、UI をラップしたクライアントが多いです。ほとんどのクライアントは UI 上でルール設定を直接変更できず、設定ファイルのアップロードや購読で管理します。これは大した問題ではなく、私の用途では設定ファイルは一度作ればほとんど変えません。問題は Surge のようなサブネットカバー機能を持つクライアントがほとんど見つからず、家と外で毎回手動でプロキシを切り替えるのは避けたいことです。

最終的にマイナーな SurfBoard というアプリを見つけました。SSID ルールをサポートしており、ルールの最上部に SSID ルールで DIRECT（直通）を追加でき、回避策として機能しました。さらにこのアプリは Surge 形式の設定ファイルも互換性があります。

::github{repo="getsurfboard/surfboard"}

ただし SurfBoard は FakeIP モードがデフォルトで変更不可で、SSID ルールで DIRECT にしても最初に FakeIP に解決されるため、全体回避ほど徹底的ではありませんが、ないよりはマシです。

### 家計簿

機種変更前にちょうど家計簿をつけ始め、今では 2 ヶ月以上続けています。日常の支出記録だけでなく、毎日資産運用の収益も記録しています。iOS には「iCost」というおすすめの家計簿アプリがあり、シンプルながら機能豊富です。ショートカットに登録して、背面タップや iPhone 16 のショートカットボタンで起動し、AI が画面を認識して主要情報を自動入力、分類だけ自分で選べばいい仕組みです。iCost は Android 版も計画中ですが、2 年以上進捗が遅いです。

中国の SNS「小紅書」には家計簿アプリを作る人が多く、独立系の三大アプリはノート、TODO、家計簿です。最終的に「钱迹（Qianji）」という老舗の家計簿アプリを見つけました。iCost より歴史が長く、iOS、MacOS、Android、Windows、さらには鸿蒙 Next までマルチプラットフォーム対応。iCost からの履歴インポートも簡単です。資産管理、クレジットカード返済、返金、多通貨、多帳簿など必要な機能は一通り揃っています（私は多帳簿は使いませんが）。[公式サイト](https://qianjiapp.com)

钱迹の自動記帳は iCost より優れている気がしますが、プラットフォームの違いかもしれません。钱迹はシステムのアクセシビリティ機能を利用し、現在開いている画面が請求書ページ（支付宝や微信）だと自動認識して小窓を表示し、素早く記帳できます。ただし画面認識の精度はまだ改善の余地があり、他のアプリから支付宝の支払い完了画面に遷移した場合は認識に失敗し、手動で支付宝の支払い履歴画面を開いて再認識させる必要があります。小さな欠点ですが許容範囲です。

### カレンダー／TODO

iOS 18 の最大のアップデートはカレンダーとリマインダーの統合で、カレンダー上で全リマインダーを直感的に確認・操作できます。iOS の優れた通知機能のおかげでリマインダーも非常にタイムリーで、生産性向上に最適な小ツールと言えます。有料アプリよりも優れていることも多いです。Android に切り替えると、まずシステム標準のカレンダーは除外しました。移行性が全くなく、次回機種変更でブランドを変えたらデータが全部消えてしまうからです。

いろいろ探した結果、Microsoft の Outlook が要件に最も合いました。メールアプリですが、カレンダー機能が意外と使いやすいです。ただし旧暦表示はできず、旧暦カレンダーを購読して表示しています。毎日いくつかの予定が表示されますが、Outlook のローカライズがあまり良くなく、伝統的な祝日や休日の振替は一切なく、さらに多くの予定を購読しなければならず、旧暦の誕生日や記念日も加えるとカレンダーはぎっしりで、重要な予定が一目でわかりにくいです。また TODO 機能は弱く、最低限の利用にとどまります。

偶然思い出した老舗の TODO アプリ「滴答清单（TickTick）」を試してみたところ、iOS のカレンダー＋TODO の最良の代替品と感じました。前述のすべての機能をネイティブにサポートしています：旧暦表示、旧暦の予定、祝日と振替休日、カウントダウン日や記念日、TODO 表示。さらに iOS の TODO で手動で四象限法にリスト分けしていたのが、滴答清单は標準でサポートしていて驚きました。唯一の難点は年間 139 元の有料会員料金ですが、それは私の問題です。

### ウォッチ

Android エコシステムに切り替えて、ついに美しいだけの無駄遣いだった Apple Watch から解放されました。もう毎日充電しなくていいのです。Oppo のスマホを使うなら、当然 Oppo のスマートウォッチを使います。Oppo Watch X2 を購入し、新品の Apple Watch とほぼ同じ価格でした。

Oppo Watch X2 は現在のスマートウォッチでは珍しい丸型文字盤を採用し、文字盤のデザインも多くがスキューモーフィズム（写実的）テーマで目を引きます。私は時計に求めるものは低く、時間が見えて、スマホの通知やアラームを同期し、睡眠データやその他の健康データを記録できれば十分です。この時計はそれらを完璧にこなします。さらに驚いたのは HRV（心拍変動）機能で、Apple Watch ではほとんど無視され、ほとんどの場合サードパーティアプリでしか表示されませんが、Oppo では特別な心身状態ページがあり、孫颖莎さんを起用した広告も展開しています。

### おわりに

これだけ試行錯誤した結果、ようやく使いやすいスマホを手に入れ、写真も彼女から認められました。完璧です。