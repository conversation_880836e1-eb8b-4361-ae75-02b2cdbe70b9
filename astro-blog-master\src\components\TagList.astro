---
import { getTagPath } from '@/i18n/path'

interface Props {
  tags: string[]
  currentTag?: string
  lang: string
}

const { tags, currentTag = '', lang } = Astro.props;
---

<div class="no-heti flex flex-wrap gap-x-3 gap-y-3.2">
  {tags.map(tag => (
    <a
      href={getTagPath(tag, lang)}
      class:list={[
        currentTag === tag
          ? 'border-secondary/80 c-primary font-medium ring-0.2'
          : 'ring-0',
          'relative inline-block border border-secondary/25 rounded-full px-3.2 py-0.7 ring-secondary/80',
          'transition-[colors,box-shadow] ease-out hover:(border-secondary/80 c-primary font-medium ring-0.2)',
      ]}
    >
      <span class="absolute inset-0 flex items-center justify-center whitespace-nowrap transition-font-weight">
        {tag}
      </span>
      <span
        class="inline-block font-medium opacity-0"
        aria-hidden="true"
      >
        {tag}
      </span>
    </a>
  ))}
</div>
