---
lang: 'zh'
---
import FriendCard from '@/components/Widgets/FriendCard.astro';

<FriendCard 
  name="<PERSON>" 
  url="https://oliverchen12.github.io" 
  avatar="https://blog-img.shinya.click/2024/5e107c1356d8cd90f1b98d170368c2df.jpg" 
  description="互联网新星" 
/>

<FriendCard 
  name="Sun Yushuo" 
  url="https://yyd-piren.github.io" 
  avatar="https://blog-img.shinya.click/2024/a63e3e016fdaf653fde08969916830eb.JPG" 
  description="风雨湿征衣" 
/>

<FriendCard 
  name="子行" 
  url="https://www.cnblogs.com/fallingdust" 
  avatar="https://blog-img.shinya.click/2024/9387729ab1b3f7f1c9a7c644d306c851.PNG" 
  description="往日痕迹" 
/>

<FriendCard 
  name="Ancy" 
  url="https://anxcye.com" 
  avatar="https://avatars.githubusercontent.com/u/91717732" 
  description="Coding with love!"
/>

<FriendCard 
  name="Zwei" 
  url="https://zwei.de.eu.org" 
  avatar="https://avatars.githubusercontent.com/u/110226580?v=4" 
  description="人生如白驹过隙，历史似长河永流"
/>

---

## 申请友链

如果您想与我交换友链，请确保您的网站符合以下条件：
1. 网站内容健康、积极向上
2. 网站能够正常访问
3. 有一定的原创内容

本博客信息
```
title: 信也のブログ
url: https://shinya.click
description: 一写代码的
avatar: https://shinya.click/logo.png
```

请首先将我的网站添加到友链中，并通过以下方式联系我：
- [Email](mailto:<EMAIL>)
- 评论区留言
- Github issue
::github{repo="senshinya/astro-blog"}

<br />
申请时请提供以下信息：
- 网站名称
- 网站链接
- 网站描述（简短介绍）
- 头像/Logo URL
