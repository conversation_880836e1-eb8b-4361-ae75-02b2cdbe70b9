{"$ref": "#/definitions/posts", "definitions": {"posts": {"type": "object", "properties": {"title": {"type": "string"}, "published": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "description": {"type": "string", "default": ""}, "updated": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "string", "format": "date"}, {"type": "integer", "format": "unix-time"}]}, "tags": {"type": "array", "items": {"type": "string"}, "default": []}, "draft": {"type": "boolean", "default": false}, "pin": {"type": "integer", "minimum": 0, "maximum": 99, "default": 0}, "toc": {"type": "boolean", "default": true}, "lang": {"type": "string", "enum": ["", "zh", "en", "es", "ja", "ru", "zh-tw"], "default": ""}, "abbrlink": {"type": "string", "default": ""}, "optimizeImages": {"type": "boolean", "default": true}, "$schema": {"type": "string"}}, "required": ["title", "published"], "additionalProperties": false}}, "$schema": "http://json-schema.org/draft-07/schema#"}