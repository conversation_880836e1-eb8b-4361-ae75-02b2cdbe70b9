---
lang: 'en'
---
import FriendCard from '@/components/Widgets/FriendCard.astro';

<FriendCard 
  name="<PERSON>" 
  url="https://oliverchen12.github.io" 
  avatar="https://blog-img.shinya.click/2024/5e107c1356d8cd90f1b98d170368c2df.jpg" 
  description="互联网新星" 
/>

<FriendCard 
  name="Sun Yushuo" 
  url="https://yyd-piren.github.io" 
  avatar="https://blog-img.shinya.click/2024/a63e3e016fdaf653fde08969916830eb.JPG" 
  description="风雨湿征衣" 
/>

<FriendCard 
  name="子行" 
  url="https://www.cnblogs.com/fallingdust" 
  avatar="https://blog-img.shinya.click/2024/9387729ab1b3f7f1c9a7c644d306c851.PNG" 
  description="往日痕迹" 
/>

<FriendCard 
  name="Ancy" 
  url="https://anxcye.com" 
  avatar="https://avatars.githubusercontent.com/u/91717732" 
  description="Coding with love!"
/>

<FriendCard 
  name="Zwei" 
  url="https://zwei.de.eu.org" 
  avatar="https://avatars.githubusercontent.com/u/110226580?v=4" 
  description="人生如白驹过隙，历史似长河永流"
/>

---

## Apply for Friendship Link

If you’d like to exchange friendship links with my blog, please make sure your website meets the following criteria:
1. Content is healthy and positive
2. Website is accessible and online
3. Has a certain amount of original content

About This Blog
```
title: Shinya’s Blog
url: https://shinya.click
description: Coder
avatar: https://shinya.click/logo.png
```

Please add my site to your friendship links first, then contact me via one of the following methods:
- [Email](mailto:<EMAIL>)
- Leave a comment
- Submit a Github Issue
::github{repo="senshinya/astro-blog"}

<br />
When applying, please provide the following information:
- Website name
- Website URL
- Brief introduction/description
- Avatar/Logo URL
