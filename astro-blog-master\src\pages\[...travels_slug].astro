---
import type { CollectionEntry } from 'astro:content'
import { getCollection } from 'astro:content';
import { defaultLocale, moreLocales } from '@/config'
import TravelLayout from '../layouts/Travel.astro';
import TravelJourney from '../components/TravelJourney.astro';

export async function getStaticPaths() {
  const travels = await getCollection('travels')

  // >>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>
  type PathItem = {
    params: { travels_slug: string }
    props: { entry: CollectionEntry<'travels'> }
  }

  const paths: PathItem[] = []

  // Default locale
  travels.forEach((travel: CollectionEntry<'travels'>) => {
    // Show drafts in dev mode only
    if ((import.meta.env.DEV || !travel.data.draft)
      && (travel.data.lang === defaultLocale || travel.data.lang === '')) {
      const slug = travel.data.abbrlink || travel.id

      paths.push({
        params: { travels_slug: `${slug}/` },
        props: {
          entry: travel,
        },
      })
    }
  })

  // More locales
  moreLocales.forEach((lang: string) => {
    travels.forEach((travel: CollectionEntry<'travels'>) => {
      // Process posts with matching language or no language specified
      if ((import.meta.env.DEV || !travel.data.draft)
        && (travel.data.lang === lang || travel.data.lang === '')) {
        const slug = travel.data.abbrlink || travel.id
        paths.push({
          params: { travels_slug: `${lang}/${slug}` },
          props: {
            entry: travel
          },
        })
      }
    })
  })

  return paths
}

const { entry } = Astro.props;
const { posttitle } = entry.data;
---

<TravelLayout title={`${posttitle}`}>
  <TravelJourney travel={entry}/>
</TravelLayout>
