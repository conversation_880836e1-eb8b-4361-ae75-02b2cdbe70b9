---
title: バイトで3年間働きながらも一定の精神的健康を保つことは、実は完全に不可能ではない
tags: ["日常","仕事", "年間総括"]
lang: ja
published: 2024-09-28T16:26:00+08:00
abbrlink: daily/work-for-3-years
description: "バイトで3年間働き、時間の流れがまるで見えないうちに加速しているかのように感じられます。急速に変化する環境に身を置きながらも、わずかな心理的バランスを見つけることができました。入社当時の戸惑いを振り返ると、若き日の自分が杭州八方城のバルコニーで遠くの山々を眺め、未来に期待と好奇心を抱いていたのを思い出します。時間が経つにつれて、職場での挑戦と成長が交錯し、忙しい中でも自分のリズムを見つけることを学んだ、独特で豊かな経験となりました。"
---
![タイトル画像](https://blog-img.shinya.click/1726828874334.jpg)

夕食時、同僚が買った車の話をしていて、突然「仕事を始めてから時間がすごく早く感じる」と呟きました。

私：「そうとも限らないよ。今日（金曜）の午後は結構辛かった」

認めたくはないけれど、ずっと学生のつもりでいる（今のところチームで最年少）ものの、学校を離れてからもう 3 年以上経っています。この 3 年間は波乱もありましたが、比較的安定していました。

バイト内には「バイトの 1 年は人間の 3 年に相当する」という古い言葉があります。この「9 年分」の時間もここに詰まっています。

### 戸惑い

インターン期間を除けば、私は 21 年 6 月末に杭州バイトに入社しました。

こちらをご覧ください：[入社 1 週間の記録](https://www.nowcoder.com/share/jump/67966291840886568)

杭州バイトは八方城の 2 棟のビルを借りていますが、全棟ではなく、両棟とも最上階の 5〜6 階だけを借りています。景色はなかなか良く、毎日バルコニーに寄りかかって遠くの EFC（余杭で最も都会的な場所）やさらに遠くの余杭南部の山々を眺めることができます。山に雨が迫る時や雨上がりの晴れた時の景色は格別です。

私は抖音ライブ部門の ToC チームに配属されました。インターンで 2 ヶ月間新人向けドキュメント作成を手伝い、チームの業務はある程度把握していました。しかしインターン終了後、卒業手続きのために 1 ヶ月間学校に戻り、再入社した時には急に仕事の割り当てがなくなっていました。そこで楽しいサボり期間を過ごし、最後の 1 ヶ月は大小週休二日制で倍の給料をもらいました。

ある日、リーダーとメンターが私を呼び、北京のある事業にボランティアで参加しないかと尋ねられました。そこは ToB プラットフォームで、私たちと同じ +2 に属し、急速に成長して人手不足だったため、+2 全体から人員を割り当てられ、私のチームに 1 名の枠が割り当てられたのです。当時私は新卒でエネルギーが有り余っており、ちょうど暇だったので、即答で引き受けました。

それ以来、北京のチームと一緒に要件対応を続けました。これは上位管理向けのプラットフォームで、QPS は非常に低いものの、主にデータを生成して上層に報告するためのもので、ToC 事業とは全く異なる視点で、ビジネス重視で性能は二の次でした。初期段階では整合性もあまり重視されず、呼び出し失敗時にはアラートを出して人が確認し、影響が小さければ修正しないこともありました。ユーザーの操作頻度も低く、日常のメンテナンスは比較的楽でした。

私が入社した頃はプラットフォームの大規模バージョンアップの時期で、北京チームからリモートでエンジニアが派遣され、要件対応を教えてくれました。最初は小さな要件ばかりで、細かい修正を繰り返しながら、プロセスに慣れていきました。

> [!TIP]
> 要件初期評価 — 要件詳細評価 — （開発介入）技術レビュー — 開発 — （テスト介入）ショーケース — テスト — ローンチレビュー — リリース

順序立てて、一つの要件を終えたら次へ。余計なことはありません。最初は技術レビューで何を話せばいいか分からず、わざわざ会議室を借りて正座していたのが、徐々に……

しかし実際には大きな成長はなく、慣れや麻痺が進み、これらのことを軽視するようになりました。オフラインのバグならまだしも、オンラインのバグでもデータを修正すれば済むと思っていました。

Golang で書く業務はほぼフリーランスのようなもので、要件の高速なイテレーションもあり、コードのリファクタリングに時間を割けず、とにかく動けば成功でした。リポジトリのコードはどんどん混沌としていきました。複雑で奇妙な要件が多く、あちこちからの借り出しで技術スタイルも統一されず、このプラットフォームは次第に「大きな泥団子」のようになりました。

でもそれが私に何の関係があるでしょうか。私は小さな要件を書くただの初心者ですから。

入社したばかりで何もかも新鮮で、週末に自主的に出社して残業することもありました。家にいても特にやることがないので、むしろ楽しかったのです。後に自分でプロジェクトを見つけて、[MYDB](https://github.com/CN-GuoZiyang/Mydb) と関連する [チュートリアル](/ja/projects/mydb/mydb0) を完成させました。バグは多く、十分にテストはできませんでしたが、やる気は満々で、夜 8〜9 時に退勤し、深夜 1〜2 時まで書き続け、シャワーを浴びてゲームをしたりお酒を飲んだりして、3〜4 時に寝る生活でした。

面白い人たちとも知り合い、小さなグループを作り、仕事がない時はチャットで雑談していました。当時のバイトは私のインターネットに対するすべての想像を満たしていました：自由で、管理はフラットで、休暇申請も理由を考える必要がなく、仕事用 PC に好きなソフトを入れられ、コロナ禍がまだ終わっていなかったため、時々リモートワークもあり、打刻や強制労働時間もなく、11 時過ぎに出社して夕食後すぐに帰宅することもありました……

### 努力

そんな感じで楽しく働いていたのが 22 年初めまでです。その間、細かい要件から徐々に領域全体をカバーできるようになりました。

アリババには古い言葉があり、解雇された人や自分を慰める時によく使われます：「変化を受け入れよ」

> 唯一変わらないのは変化だけだ

プラットフォームは急速に成長し、事業はライブ配信から抖音全体に拡大しました。製品の調整により、元々北京の開発チームがプラットフォームを引き継ぎ、引き継いだのは私の元のチームでした。

そこで引き継ぎ作業が始まり、毎日の仕事は新人（新チームのメンバー）を連れて要件をこなし、様々な共有を行うことでした。私はその頃すでに予算モジュールを担当しており、一度共有会も開催しました。文書を棒読みして感情はなかったものの、会社での初めての共有でした（正社員登用時の面接は不要でした）。

北京のメンバーが徐々に撤退し、人手不足が一気に深刻化しました。予算モジュール以外にも、プロジェクト領域全体が私の手に押し付けられました。ちょうど製品から大きな要件が出され、プロジェクト領域の最初の要件はほぼ全面的な書き直しでした。

要件は春節休暇と重なり、緊急で、休暇中の 30 日と元旦以外は図書館でコードを書き続けました。理解の誤差などもあり（まだ未熟でした）、コードは 3〜4 回も書き直しましたが、なんとか休暇明け最初のリリースに間に合わせました。

その後も様々な過去の問題の清算やリファクタリングレビュー、新サービスへのコード移行（ちなみに 2022 年に開始した新サービス移行は未だ完了していません）で、足が地に着かないほど忙しかったです。

その時期は非常に不安で、残業も激しく、日常の問題処理で 10 時過ぎまで働き、10 時前に帰宅したことはほとんどありませんでした。代わりに報復的な徹夜や報復的な娯楽をしていました。典型的な娯楽は金曜の夜に会社の会議室で酒を飲み、土曜の早朝に帰宅して寝ることです 😅

不思議なことに、残業中はいつも私のチームリーダーに会いました。10 時か 11 時頃、どちらかが「帰ろう」と声をかけ合うのです。私は通勤の便を考えて会社のあるマンションに引っ越し、毎日地下駐車場を通って通勤していますが、彼は毎日 50 分から 1 時間かけて通勤しています。

これが家庭を持つことの代償なのでしょう。

帰宅が遅すぎて、日常の開発や勉強はほぼ止まり、家に帰ると酒を飲んで寝るだけでした。飲む酒も自作のカクテルからウォッカやウイスキーのストレートに変わり、飲酒の目的も「美味しい・ほろ酔い」から「睡眠補助・不安解消」へと変わりました。

### 転換

> 時には変わらない生活の方が、未知のリスクよりも安心だと感じることもある

リスクは急にやってきました。

22 年の中頃、私たちのチームに新しい +2 が来て、雰囲気が微妙に変わりました。以前は気軽だった非公式ミーティングが正式化され、要件のプロセスも複雑になり、簡単なステップに無数の審査が追加され、あらゆる決定が多層の承認を経るようになりました。

「バグ比人天」「人効率比」「コード複雑度」などの新語が生まれました。指標が高いか低いかで、次の週次会議や隔週会議で批判されることが多くなりました。プロダクト・開発・テストの関係はもはや和やかではなく、会議では舌戦や策略、責任転嫁が繰り返され、疲弊しきってコードを書くことさえ贅沢になりました。多くやれば多くミスし、やらなければミスしない、できるだけ責任を回避するのが生き残る術となりました。かつては「オンラインバグはデータ修正で済む」だったのが、今では「回帰バグ 1 つで死刑宣告」になりました。

この頃から HC の縮小によりチームの人員削減が始まり、知っている多くの人が自主的または強制的に去っていきました。かつて毎日一緒に食事し、サボり、帰宅していた同僚も、今では週に一度会うかどうかで、仕事の孤独感が増していきました。

<img src="https://blog-img.shinya.click/2024/630b780569e7aa8fa40e1ecc6a189b40.png" style="width: 50%"/>

そんな状況で、感情の身体化か、長年の徹夜と飲酒の影響か、あるいはその両方か、体が悲鳴を上げ始めました。詳細は：[慢性胃炎治療の道](/ja/daily/anti-chronic-gastritis) をご覧ください。

体重は急激に減少し（一ヶ月で約 6kg）、痛みを感じて、ついに健康モードに切り替えました！

毎日 8 時半に起きて会社で朝食をとり、夜 6 時過ぎに夕食を済ませてすぐ帰宅し、帰宅後は 30 分ランニング、12 時前に就寝（現在も努力中）という健康的な生活を送っています。

> [!CAUTION]
> 頑張る？命がなければ頑張れません！

幸いにも、前述の激変で多くのプロダクトが消え、要件量は激減しました。上層からは「整活できないなら人をいじれ」というモードも始まり、様々な細かい制約や規範が増えましたが、今の +2 はアリババ色が強く、意味不明なチームビルディングや個人ショーを好み（別の意味での苦痛）、プラットフォームは低頻度メンテナンス期に入り、細かいことは減りました。

外の市場は厳しいので、とりあえずこのままやっていきます。

### あとがき

本来、この 3 周年の総括は 5〜6 月に書く予定でしたが、その頃は製品の大変動と体調不良が重なり、この文書のタイトルは 3 ヶ月間メモに眠っていました。最近になって体調が安定し、ようやくまとめを再開しました。しかしこの 3 ヶ月で、心境は積極的な前進から「とりあえずこのままやる」に変わり、世の中の変化の激しさを痛感しました。

しかし幸いにも、ある程度の精神的健康は保てています。最近は心理状態の検査やスケジュール管理を研究し、自律的な人間を目指しています。現時点の成果は、時間がどこで浪費されているかを可視化できることです。今後、この分野の成果について記事を書く予定です。

ここ数年、留学したいという気持ちはくすぶり続けており、特に不満がある時は強くなります。忙しい一日の深夜、この思いはまるで蔓草のように心を覆います。この 3 年で辞職に最も近づいた瞬間は、病院で胃カメラを待ちながらも飛書のメッセージに返信してオンコール対応していた時でした。自問します。「この給料のために払っている代償は本当に価値があるのか」と。しかし冷静になると、ため息をついてその考えを押し込めます。

3 年後、今の自分を後悔したり憎んだりするかもしれません。しかしそれも選択の呪いかもしれません。

> どんな選択をしても、あるいは選択しなくても、代償は必ず払わなければならないのです。