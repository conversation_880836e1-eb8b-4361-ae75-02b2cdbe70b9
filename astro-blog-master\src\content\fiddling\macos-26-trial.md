---
title: macOS 26 / iPadOS 26 尝鲜
tags: ["折腾","macos","ipados","Liquid Glass"]
lang: zh
published: 2025-06-10T20:18:00+08:00
abbrlink: fiddling/macos-26-trial
description: "macOS 和 iPadOS 26 带来了全新的 Liquid Glass 设计语言，界面风格焕然一新，图标和窗口效果更具现代感，尤其是在 iPad 上实现的窗口化应用模式，标志着其向生产力工具的转变。然而，部分设计如透明的控制中心和对启动台的整合引发了一些争议，用户体验仍需进一步优化。这次更新虽有不足，却为未来的发展奠定了基础，值得期待。"
---

又是一年科技春晚 WWDC 25 在今日凌晨召开，众所周知科技以换皮为本，除了重点介绍了<del>国行用不到</del>国人不屑于使用的 Apple Intelligence 之外，最大的更新就是各平台系统发布了最新版并统一以年份命名，以及随之而来的 Liquid Glass 设计语言了。

原本我是不关心 WWDC 的，通常都是保守地等待正式版更新。但是今天早上铺天盖地的科技博主测评和新闻，和各种群嘲梗图，让我很难不关注，简单了解过后，决定更新下尝鲜。

![据说 Liquid Glass 的灵感来自于此](https://blog-img.shinya.click/2025/10a1a04fe5272425d5df103b0403286b.png)

由于我的 iPhone 已经卖掉了（见 [上一篇](/fiddling/one-month-using-android)），所以这次体验范围为 macOS 和 iPadOS —— 这两个系统的吐槽似乎相对少一些。

首先最直观的感受当然也就是 UI 上的变化，本次多平台统一使用的设计语言 Liquid Glass，据说最早来自于 Vision OS。当然我的 Vision OS 也卖掉了，所以没法直接比较，不过直观感受上确实有一种 Vision OS 的风味，窗口都使用了毛玻璃效果，图标也统一改成了拟物（毛玻璃切片？姑且叫这个名字）风格。

![说实话有一点山寨感，有点上古山寨机的风味了](https://blog-img.shinya.click/2025/a8cf24e6e65fb60e3b12459b13dd7b80.png)

iPad 上图标统一倒是效果不错，第三方应用的图标也有做效果转换，看起来风格很统一。

![iPad 图标](https://blog-img.shinya.click/2025/7c1c0ec885660f91df41053898c0cd88.PNG)

窗口也更换为了统一风格，另外还统一了 Apple Music 的视觉效果，看起来也更有科技感，不像之前 iTunes 风格，看起来就卡卡的（当然实际体验两说）。

![统一窗口算得上赏心悦目](https://blog-img.shinya.click/2025/7b73e36fb041c8dc5e7ff9aad0faeb57.png)

macOS 上被人喷得最多的，倒不是 Liquid Glass（UI 负反馈主要来自于 iPhone），而是启动台 LaunchPad 下线了，功能融合进了 SpotLight，直接点击 Dock 上的 App 按钮，和 Cmd + Space 效果相同，弹出 SpotLight 搜索框。

![SpotLight](https://blog-img.shinya.click/2025/4b190a366a67d3f5dc14bea1491ebb92.png)

需要再次按 Cmd + 1 才会展示出全部 App。

![全部 App 页面](https://blog-img.shinya.click/2025/9b74ffbd1b34ef15ef5b320135c653d7.png)

这个设计就有点有病了，尤其是对于我这种老年痴呆症患者来说，App 的名字根本记不住，勉强看到图标才能想起来功能。不知道 LaunchPad 和 Apple 有什么仇。说不定过几天就有独立开发者开发出一个 LaunchPad 替代品，然后收我 99。

Quiz：相册 app 叫什么，相册还是图库？

答案：照片（搜了好几个都不对）

另外一个小更新就是点击键盘上调整音量按键和调整亮度按键时，不再会弹出全屏的提示，而是会在状态栏上展示一个小弹框，有点像触摸屏的逻辑。

![音量控制](https://blog-img.shinya.click/2025/85841f6a20fe5bc87d2e4e7036e7e1ec.png)
![亮度控制](https://blog-img.shinya.click/2025/c781da9ec7dfac96e7588a8faa047df5.png)

因为没有 iPhone，最经典的 Liquid Glass 我只能在 iPad 上体验了。我的 iPad 是 iPad Pro 11 英寸，第四代，使用的是 M2 芯片。

一些浮夸的锁屏时钟可以拉长之类的功能就不体验了，聚焦点主要在通知栏和控制中心。通知栏下拉的时候有实时的玻璃特效，仿佛是真的在拖动一个玻璃一般，效果很惊艳，就是不知道是否有性能要求，老 iPad 能不能轻松跑起来。

![注意边界效果](https://blog-img.shinya.click/2025/acbdd751ce6b1ca0c76301c826274aea.PNG)

下面就是被人吐槽最多的控制中心了，透明度太高导致辨识度很低，估计后续版本可能会调整，或者允许用户自定义。

![是有些看不清楚的](https://blog-img.shinya.click/2025/e4cd043a2db8acf7c8a5d12f3c90065e.PNG)

当然，除了一无是处，这次更新还是有一些可取之处的。最大的亮点就是 iPadOS 更新的窗口化 App 模式。老版本的 iPad App 只能全屏幕或台前调度，屏幕上只能保留一个 App，或者使用分屏进行一些简单的多任务操作。窗口化 App 的操作逻辑则完全和 macOS 一致了，可以随意调整窗口大小、最大化、最小化，窗口互相覆盖等等。如果外接大屏，完全可以当作电脑使用，算是 iPad 成为真正的生产力工具的重要一步。

![不黑不吹，这是真的 Legendary](https://blog-img.shinya.click/2025/4846d5e04b7811a0435573f43d26dfec.PNG)

这些就是我对本次更新的一些直观感受了，说实话我还是很看好这个设计语言的，Windows Vista 受限于当时的机能未能实现的未来，终于被 Apple 实现了。一些比较严重的问题和吐槽，在后续的更新中应该会得到解决。当前只是 macOS 26 / iPadOS 26 的第一个开发者预览版，未来可期。